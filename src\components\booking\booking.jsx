import React, { useState, useEffect, Fragment, use<PERSON>ontext, useMemo, useCallback } from "react";
import { useHistory } from "react-router-dom";
import {
    Al<PERSON>,
    Container,
    Row,
    Col,
    Button,
    Form,
    FormGroup,
    InputGroup,
    Label,
    Input,
    InputGroupAddon,
    InputGroupText,
    Modal,
    ModalBody,
    FormFeedback,
    TabContent,
    TabPane,
    // FormText, // Unused import
} from "reactstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { getUnits } from "../../api_controllers/units_controller";
import { getLocations } from "../../api_controllers/locations_controller";
import { getAirlines } from "../../api_controllers/airlines_controller";
import { getRateByPlaces } from "../../api_controllers/rates_controller";
import { postReservation } from "../../api_controllers/reservations_controller";
// import { sendMail, parserEmailData } from "../../api_controllers/email_controller";

import "moment/locale/cs.js";
import "moment/locale/es.js";
import "moment/locale/fr.js";
import "moment/locale/nl.js";
import moment from "moment";

import LoadingOverlay from "react-loading-overlay";

import Select from "react-select";
import { registerLocale } from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import es from "date-fns/locale/es";
import en from "date-fns/locale/en-US";
import "./validation.css";

import Swal from "sweetalert2";
import Summary from "./summary";
import Sidebar from "./sidebar";
import { DateTimePickerForm } from "./dateTime-picker-form";
import { ReservationContext } from "../../context/reservation.context";
import { GetStopSuperMarket } from "./get-stop-super-market";

registerLocale("es", es);
registerLocale("en", en);

export default function Booking({ lang, params }) {
    let history = useHistory();
    let _lang = new URLSearchParams(window.location.search).get("lang");
    const {reservation /* , setReservation, finalRate */ } = useContext(ReservationContext);

    // Desestructuramos valores del contexto de reservación
    const {
        is_stop_at_store, // Used in useState initialization
        extra_service_selected,
        has_promotion,
        promotion
        // promotion_type // Unused variable
    } = reservation;

    // Configuración de idioma
    if (_lang !== null) {
        lang = _lang;
    }

    if (lang === "eng") {
        moment.locale("en");
    } else {
        moment.locale("es");
    }

    // Opciones de tipo de viaje
    const tripTypes = useMemo(() => [
        {
            value: "One Way",
            label: lang === "eng" ? "One Way" : "Solo Ida",
        },
        {
            value: "Round Trip",
            label: lang === "eng" ? "Round Trip" : "Ida y Vuelta",
        },
    ], [lang]);

    const tripTypes2 = useMemo(() => [
        {
            value: "Round Trip",
            label: lang === "eng" ? "Round Trip" : "Ida y Vuelta",
        },
    ], [lang]);

    // Estados para datos de la reservación
    const [units, setUnits] = useState([]);
    const [airlines, setAirlines] = useState([]);
    const [airlinesOptions, setAirlinesOptions] = useState([]);
    const [unitsOptions, setUnitOptions] = useState([]);
    const [unitSelected, setUnitSelected] = useState({ image: "" });
    const [numPassengers, setNumPassengers] = useState([]);
    const [locations, setLocations] = useState([]);
    const [filteredPickupLocations, setFilteredPickupLocations] = useState([]);
    const [modalConfirmation, setModalConfirmation] = useState(false);
    const [loaderController, setLoaderController] = useState(false);
    const [totalPayment, setTotalPayment] = useState(0);
    const [tarifaBase, setTarifaBase] = useState(0);

    // Valores auxiliares para los selectores
    const [auxTripType, setAuxTripType] = useState(tripTypes[1]); // Round Trip por defecto
    const [auxUnit, setAuxUnit] = useState(null);
    const [auxPickup, setAuxPickup] = useState(null);
    const [auxDestination, setAuxDestination] = useState(null);
    const [auxArrivalAirline, setAuxArrivalAirline] = useState(null);
    const [auxDepartureAirline, setAuxDepartureAirline] = useState(null);

    // Estados para validación y control de UI
    const [firstFormValidator /* , setFirstFormValidator */] = useState(false);
    const [validRate, setValidRate] = useState(true);
    const [idBooking, setIdBooking] = useState('');
    const [form, setForm] = useState(reservation);
    const [isCalculatingRate, setIsCalculatingRate] = useState(false);

    // Estados para validación de campos
    const [validationErrors, setValidationErrors] = useState({
        fullname: false,
        member_id: false,
        email: false,
        cellphone: false,
        pickup_location: false,
        destination_location: false,
        pickup_date: false,
        pickup_time: false,
        arrival_airline: false,
        arrival_flight_number: false,
        departure_date: false,
        departure_airline: false,
        departure_flight_number: false
    });

    // Estado para controlar si el formulario ha sido enviado
    const [formSubmitted, setFormSubmitted] = useState(false);

    // Estados para control de tipo de viaje
    const [tripValidator, setTripValidator] = useState(false);
    const [, setRoundTripSelected] = useState(false); // Unused variable but setter is used
    const [, setRoundTripValidation] = useState(false); // Unused variable but setter is used
    const [, setIsAirportDestination] = useState(false); // Unused variable but setter is used

    const [isOneWay, setIsOneWay] = useState(reservation.trip_type === 'One Way');
    const [isRoundTrip, setIsRoundTrip] = useState(reservation.trip_type === 'Round Trip');
    const [isOneWayAirportDestination, setIsOneWayAirportDestination] = useState(false);
    const [, setIsStopatStore] = useState(is_stop_at_store); // Unused variable but setter is used
    const [isServiceActive, setIsServiceActive] = useState(false);
    const [serviceSelected, setServiceSelected] = useState(extra_service_selected);

    // Efecto para manejar cambios en el validador de viaje
    useEffect(() => {
        if (tripValidator === true) {
            setIsOneWay(false);
            setIsRoundTrip(true);
            setIsOneWayAirportDestination(true);
            setForm(prevForm => ({
                ...prevForm,
                trip_type: 'Round Trip',
            }));
            setAuxTripType(tripTypes2[0]); // Round Trip option
        }
    }, [tripValidator, tripTypes2]);

    // Efecto para detectar si el destino es el aeropuerto
    useEffect(() => {
        setIsAirportDestination(form.destination_location === 'Airport SJD');
    }, [form.destination_location]);

    // Generar código de reservación único
    const getCodeAux = () => {
        const year = moment().format("YY");
        const month = moment().format("MM");
        const day = moment().format("DD");
        const random = Math.floor(Math.random() * 99999).toString().padStart(5, '0');
        setIdBooking(`TR${year}${month}${day}-${random}`);
    };

    const getAllAirlines = () => {
        getAirlines().then((response) => {
            if (response.data.length > 0) {
                let _airlines = response.data;
                setAirlines(_airlines);
                let auxAirlinesOptions = [];
                _airlines.forEach((item) => {
                    auxAirlinesOptions.push({
                        label: item.label,
                        value: item.name,
                    });
                });
                setAirlinesOptions(auxAirlinesOptions);
            }
        });
    };

    const getAllUnits = useCallback(() => {
        getUnits().then((response) => {
            if (response.data.length > 0) {
                let _units = response.data;
                setUnits(_units);
                setUnitSelected(_units[0]);

                let auxUnitOptions = [];
                _units.forEach((item) => {
                    auxUnitOptions.push({
                        label: item.label,
                        value: item.label,
                        id_unit: item.id_unit,
                    });
                });

                setUnitOptions(auxUnitOptions);
                setForm(prevForm => ({
                    ...prevForm,
                    unit: _units[0].label,
                    passenger_number: _units[0].capacity,
                    unit_id: _units[0].id_unit,
                }));
                setAuxUnit(auxUnitOptions[0]);
            }
        });
    }, []);

    const getAllLocations = () => {
        getLocations().then((response) => {
            setLocations(response.data);
        });
    };

    // Función para filtrar las ubicaciones de pickup según el tipo de viaje
    const getFilteredPickupLocations = useCallback(() => {
        if (form.trip_type === "Round Trip") {
            // Para Round Trip, solo permitir Airport SJD
            return locations.filter(locationGroup => {
                return locationGroup.options.some(option =>
                    option.value === "Airport SJD" && option.id_zone === 1
                );
            }).map(locationGroup => ({
                ...locationGroup,
                options: locationGroup.options.filter(option =>
                    option.value === "Airport SJD" && option.id_zone === 1
                )
            }));
        } else {
            // Para One Way, mostrar todas las ubicaciones
            return locations;
        }
    }, [locations, form.trip_type]);

    // Efecto para actualizar las opciones filtradas de pickup cuando cambian las ubicaciones o el tipo de viaje
    useEffect(() => {
        const filtered = getFilteredPickupLocations();
        setFilteredPickupLocations(filtered);
    }, [getFilteredPickupLocations]);

    // Efecto para establecer automáticamente Airport SJD como pickup cuando es Round Trip
    useEffect(() => {
        if (form.trip_type === "Round Trip" && locations.length > 0 && !form.pickup_location) {
            const airportSJDOption = locations.find(locationGroup =>
                locationGroup.options.some(option =>
                    option.value === "Airport SJD" && option.id_zone === 1
                )
            )?.options.find(option =>
                option.value === "Airport SJD" && option.id_zone === 1
            );

            if (airportSJDOption) {
                setForm(prevForm => ({
                    ...prevForm,
                    pickup_location: airportSJDOption.value,
                    pickup_id: airportSJDOption.id_zone,
                }));
                setAuxPickup(airportSJDOption);

                // Clear validation error for pickup location
                setValidationErrors(prev => ({
                    ...prev,
                    pickup_location: false
                }));
            }
        }
    }, [form.trip_type, locations, form.pickup_location]);

    // Inicializar código de reservación al cargar el componente
    useEffect(() => {
        getCodeAux();
    }, []);

    // Cargar datos iniciales
    useEffect(() => {
        getAllUnits();
        getAllLocations();
        getAllAirlines();
    }, [getAllUnits]);

    // Actualizar opciones de pasajeros cuando cambia la capacidad del vehículo
    useEffect(() => {
        if (form.passenger_number) {
            const aux_passenger_capacity = Array.from(
                { length: form.passenger_number },
                (_, index) => index + 1
            );
            setNumPassengers(aux_passenger_capacity);
        }
    }, [form.passenger_number]);
    // Efecto para calcular tarifas cuando cambian los datos de la reservación
    useEffect(() => {
        // Verificar que tenemos todos los datos necesarios para calcular la tarifa
        if (form.pickup_id && form.destination_id && form.unit_id) {
            // Verificar si pickup y destination son iguales
            if (form.pickup_id === form.destination_id) {
                // Si son iguales, no calcular tarifa y mostrar error
                setTotalPayment(0);
                setValidRate(false);
                setIsCalculatingRate(false);
                return;
            }

            // Activar indicador de cálculo
            setIsCalculatingRate(true);

            // Determinar el orden de los parámetros según si el destino es el aeropuerto
            const isAirportDestination = form.destination_id === 1;
            const originId = isAirportDestination ? form.destination_id : form.pickup_id;
            const destinationId = isAirportDestination ? form.pickup_id : form.destination_id;

            // Obtener la tarifa
            getRateByPlaces(originId, destinationId, form.unit_id)
                .then((response) => {
                    if (response.data.length === 0) {
                        setTotalPayment(0);
                        setValidRate(false);

                        // Desactivar servicios extra si la tarifa no es válida
                        if (isServiceActive) {
                            setIsServiceActive(false);
                            setIsStopatStore(false);
                        }
                    } else {
                        setValidRate(true);

                        // Establecer el precio según el tipo de viaje
                        const baseRate = form.trip_type === "One Way"
                            ? Number(response.data[0].oneWay)
                            : Number(response.data[0].roundTrip);
                        setTarifaBase(baseRate);
                        // debugger;
                        // Verificar si el destino es el aeropuerto, en cuyo caso no se permiten servicios extra
                        const isAirportDestination = form.destination_location === 'Airport SJD';

                        if (isAirportDestination && isServiceActive) {
                            // Si el destino es el aeropuerto, desactivar servicios extra
                            setIsServiceActive(false);
                            setIsStopatStore(false);
                            setServiceSelected({});
                            if (has_promotion) {
                                setTotalPayment(Number(baseRate) - (Number(baseRate) * Number(promotion) / 100)); // return tarifa c / promocion
                            } else {
                                setTotalPayment(Number(baseRate)); // return tarifa neta
                            }
                            // debugger;
                        } else if (isServiceActive && serviceSelected && serviceSelected.price) {
                            // Si hay un servicio extra activo y el destino no es el aeropuerto, añadir su precio
                            if (has_promotion) {
                                setTotalPayment(Number(baseRate) - (Number(baseRate) * Number(promotion) / 100) + Number(serviceSelected.price)); // return tarifa c / promocion
                            } else {
                                setTotalPayment(Number(baseRate) + Number(serviceSelected.price)); // return tarifa neta
                            }
                        } else if (isServiceActive && !serviceSelected) {
                            setTotalPayment(Number(baseRate) + Number(serviceSelected.price)); // return tarifa neta
                        } else {
                            // Si no hay servicios extra, establecer solo la tarifa base
                            if (has_promotion) {
                                setTotalPayment(Number(baseRate) - (Number(baseRate) * Number(promotion) / 100)); // return tarifa c / promocion
                                // debugger;
                            } else {
                                setTotalPayment(Number(baseRate)); // return tarifa neta
                                // debugger;
                            }
                        }
                    }
                    // Desactivar indicador de cálculo
                    setIsCalculatingRate(false);
                })
                .catch(error => {
                    // console.error("Error al obtener tarifas:", error);
                    setTotalPayment(0);
                    setValidRate(false);

                    // Desactivar servicios extra en caso de error
                    if (isServiceActive) {
                        setIsServiceActive(false);
                        setIsStopatStore(false);
                    }

                    // Desactivar indicador de cálculo
                    setIsCalculatingRate(false);
                });
        } else {
            // Si no tenemos todos los datos necesarios, no hay tarifa válida
            setValidRate(false);
        }

        // Actualizar estados de tipo de viaje
        const isRoundTrip = form.trip_type === "Round Trip";
        setRoundTripSelected(isRoundTrip);

        // Actualizar validación de viaje redondo si el origen es el aeropuerto
        const isAirportPickup = form.pickup_location === "Airport SJD";
        setRoundTripValidation(isAirportPickup && isRoundTrip);
    }, [form.pickup_id, form.destination_id, form.unit_id, form.trip_type, form.pickup_location, form.destination_location, isServiceActive, serviceSelected, has_promotion, promotion]);

    // Manejar cambios en el tipo de viaje (One Way / Round Trip)
    const handleChangeTripType = (selected) => {
        // Guardar el estado actual de servicios extra
        const wasServiceActive = isServiceActive;
        const previousService = serviceSelected;

        if (selected.value !== "One Way") {
            // Cambiar a Round Trip
            setIsOneWay(false);
            setIsRoundTrip(true);
            setAuxTripType(tripTypes[1]);

            // Para Round Trip, establecer automáticamente Airport SJD como pickup location
            const airportSJDOption = locations.find(locationGroup =>
                locationGroup.options.some(option =>
                    option.value === "Airport SJD" && option.id_zone === 1
                )
            )?.options.find(option =>
                option.value === "Airport SJD" && option.id_zone === 1
            );

            // Actualizar el formulario
            const updatedForm = {
                ...form,
                trip_type: "Round Trip",
            };

            // Si encontramos Airport SJD, establecerlo como pickup location
            if (airportSJDOption) {
                updatedForm.pickup_location = airportSJDOption.value;
                updatedForm.pickup_id = airportSJDOption.id_zone;
                setAuxPickup(airportSJDOption);

                // Clear validation error for pickup location
                setValidationErrors(prev => ({
                    ...prev,
                    pickup_location: false
                }));
            }

            setForm(updatedForm);

            // Desactivar temporalmente el servicio extra mientras se recalcula la tarifa
            if (wasServiceActive) {
                setIsServiceActive(false);

                // Programar la reactivación del servicio después de que se actualice la tarifa
                setTimeout(() => {
                    // Solo reactivar si el destino no es el aeropuerto
                    if (form.destination_location !== 'Airport SJD') {
                        setIsServiceActive(true);
                        setIsStopatStore(true);
                        setServiceSelected(previousService);
                    }
                }, 100);
            }
        } else {
            // Cambiar a One Way
            setIsOneWay(true);
            setIsRoundTrip(false);
            setIsAirportDestination(false);
            setAuxTripType(tripTypes[0]);

            // Actualizar el formulario
            setForm({
                ...form,
                trip_type: selected.value,
            });

            // Desactivar temporalmente el servicio extra mientras se recalcula la tarifa
            if (wasServiceActive) {
                setIsServiceActive(false);

                // Programar la reactivación del servicio después de que se actualice la tarifa
                setTimeout(() => {
                    // Solo reactivar si el destino no es el aeropuerto
                    if (form.destination_location !== 'Airport SJD') {
                        setIsServiceActive(true);
                        setIsStopatStore(true);
                        setServiceSelected(previousService);
                    }
                }, 100);
            }
        }
    };

    // Manejar cambios en el vehículo seleccionado
    const handleChangeVehicle = (selected) => {
        // Guardar el estado actual de servicios extra
        const wasServiceActive = isServiceActive;
        const previousService = serviceSelected;

        // Encontrar el vehículo seleccionado
        let unit_selected = units.filter((item) => {
            return item.label === selected.value;
        });

        if (unit_selected.length === 0) return;

        // Actualizar el vehículo seleccionado
        setUnitSelected(unit_selected[0]);

        // Actualizar el formulario
        setForm({
            ...form,
            unit: unit_selected[0].label,
            unit_id: unit_selected[0].id_unit,
            passenger_number: unit_selected[0].capacity,
        });

        // Actualizar el selector auxiliar
        setAuxUnit(selected);

        // Desactivar temporalmente el servicio extra mientras se recalcula la tarifa
        if (wasServiceActive) {
            setIsServiceActive(false);

            // Programar la reactivación del servicio después de que se actualice la tarifa
            setTimeout(() => {
                // Solo reactivar si el destino no es el aeropuerto
                if (form.destination_location !== 'Airport SJD') {
                    setIsServiceActive(true);
                    setIsStopatStore(true);
                    setServiceSelected(previousService);
                }
            }, 100);
        }
    };

    const handleChangeArrivalAirline = (selected) => {
        let airline_selected = airlines.filter((item) => {
            return item.name === selected.value;
        });
        setForm({
            ...form,
            arrival_airline: airline_selected[0].name,
        });

        // Clear validation error for arrival airline
        setValidationErrors(prev => ({
            ...prev,
            arrival_airline: false
        }));

        setAuxArrivalAirline(selected);
    };

    const handleChangeDepartureAirline = (selected) => {
        let airline_selected = airlines.filter((item) => {
            return item.name === selected.value;
        });

        setForm({
            ...form,
            departure_airline: airline_selected[0].name,
        });

        // Clear validation error for departure airline
        setValidationErrors(prev => ({
            ...prev,
            departure_airline: false
        }));

        setAuxDepartureAirline(selected);
    };

    const handleChange = (event) => {
        const { name, value } = event.target;

        // Update form state
        setForm({
            ...form,
            [name]: value,
        });

        // Clear validation error for this field if it has a valid value
        if (value) {
            // Special validation for email and phone
            if (name === 'email' && !validateEmail(value)) {
                return; // Don't clear error if email is invalid
            }

            if (name === 'cellphone' && !validatePhone(value)) {
                return; // Don't clear error if phone is invalid
            }

            // Clear the validation error for this field
            setValidationErrors(prev => ({
                ...prev,
                [name]: false
            }));
        }
    };

    // Manejar cambios en la ubicación de recogida
    const handleChangePickup = (pickup_selected) => {
        // Verificar si se está seleccionando la misma ubicación que el destino
        if (form.destination_id && pickup_selected.id_zone === form.destination_id) {
            Swal.fire("",
                lang === "eng"
                    ? "Pickup and destination locations cannot be the same. Please select different locations."
                    : "Las ubicaciones de recogida y destino no pueden ser iguales. Por favor seleccione ubicaciones diferentes.",
                "warning"
            );
            return;
        }

        // Guardar el estado actual de servicios extra
        const wasServiceActive = isServiceActive;
        const previousService = serviceSelected;

        // Actualizar el formulario
        setForm({
            ...form,
            pickup_location: pickup_selected.value,
            pickup_id: pickup_selected.id_zone,
        });

        // Clear validation error for pickup location
        setValidationErrors(prev => ({
            ...prev,
            pickup_location: false
        }));

        // Actualizar el selector auxiliar
        setAuxPickup(pickup_selected);

        // Desactivar temporalmente el servicio extra mientras se recalcula la tarifa
        if (wasServiceActive) {
            setIsServiceActive(false);

            // Programar la reactivación del servicio después de que se actualice la tarifa
            setTimeout(() => {
                // Solo reactivar si el destino no es el aeropuerto
                if (form.destination_location !== 'Airport SJD') {
                    setIsServiceActive(true);
                    setIsStopatStore(true);
                    setServiceSelected(previousService);
                }
            }, 100);
        }
    };

    // Manejar cambios en la ubicación de destino
    const handleChangeDestination = (destination_selected) => {
        // Verificar si se está seleccionando la misma ubicación que el pickup
        if (form.pickup_id && destination_selected.id_zone === form.pickup_id) {
            Swal.fire("",
                lang === "eng"
                    ? "Pickup and destination locations cannot be the same. Please select different locations."
                    : "Las ubicaciones de recogida y destino no pueden ser iguales. Por favor seleccione ubicaciones diferentes.",
                "warning"
            );
            return;
        }

        // Guardar el estado actual de servicios extra
        const wasServiceActive = isServiceActive;
        const previousService = serviceSelected;

        // Si el destino es el aeropuerto (id_zone === 1), activamos el validador de viaje
        const isAirportDestination = destination_selected.id_zone === 1;
        setTripValidator(isAirportDestination);

        // Actualizar el formulario
        setForm({
            ...form,
            destination_location: destination_selected.value,
            destination_id: destination_selected.id_zone,
        });

        // Clear validation error for destination location
        setValidationErrors(prev => ({
            ...prev,
            destination_location: false
        }));

        // Actualizar el selector auxiliar
        setAuxDestination(destination_selected);

        // Si el destino es el aeropuerto, desactivar servicios extra
        if (isAirportDestination && wasServiceActive) {
            setIsServiceActive(false);
            setIsStopatStore(false);
            setServiceSelected({});
        }
        // Si el destino no es el aeropuerto y había un servicio activo, reactivarlo después de recalcular la tarifa
        else if (!isAirportDestination && wasServiceActive) {
            setIsServiceActive(false);

            setTimeout(() => {
                setIsServiceActive(true);
                setIsStopatStore(true);
                setServiceSelected(previousService);
            }, 100);
        }
    };
    // Funciones de validación
    const validateEmail = (email) => {
        const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    };

    const validatePhone = (phone) => {
        // Validar que sea un número de 10 dígitos
        const re = /^\d{10}$/;
        return re.test(String(phone));
    };

    // Abrir modal de confirmación
    const toggleModalConfirmation = () => {
        // Marcar el formulario como enviado
        setFormSubmitted(true);

        // Reiniciar errores de validación
        let errors = {
            fullname: false,
            member_id: false,
            email: false,
            cellphone: false,
            pickup_location: false,
            destination_location: false,
            pickup_date: false,
            pickup_time: false,
            arrival_airline: false,
            arrival_flight_number: false,
            departure_date: false,
            departure_airline: false,
            departure_flight_number: false
        };

        let hasErrors = false;

        // Validar campos comunes
        if (!form.fullname) {
            errors.fullname = true;
            hasErrors = true;
        }

        if (!form.member_id) {
            errors.member_id = true;
            hasErrors = true;
        }

        if (!form.email || !validateEmail(form.email)) {
            errors.email = true;
            hasErrors = true;
        }

        if (!form.cellphone || !validatePhone(form.cellphone)) {
            errors.cellphone = true;
            hasErrors = true;
        }

        if (!form.pickup_location) {
            errors.pickup_location = true;
            hasErrors = true;
        }

        if (!form.destination_location) {
            errors.destination_location = true;
            hasErrors = true;
        }

        if (!form.pickup_date) {
            errors.pickup_date = true;
            hasErrors = true;
        }

        if (!form.pickup_time) {
            errors.pickup_time = true;
            hasErrors = true;
        }

        // Solo validar aerolínea y número de vuelo si no es un viaje desde el aeropuerto
        if (!tripValidator) {
            if (!form.arrival_airline) {
                errors.arrival_airline = true;
                hasErrors = true;
            }

            if (!form.arrival_flight_number) {
                errors.arrival_flight_number = true;
                hasErrors = true;
            }
        }

        // Validar campos adicionales para viaje redondo o desde el aeropuerto
        if (form.trip_type !== "One Way" || isOneWayAirportDestination) {
            if (!form.departure_date) {
                errors.departure_date = true;
                hasErrors = true;
            }

            if (!form.departure_airline) {
                errors.departure_airline = true;
                hasErrors = true;
            }

            if (!form.departure_flight_number) {
                errors.departure_flight_number = true;
                hasErrors = true;
            }
        }

        // Actualizar estado de errores
        setValidationErrors(errors);

        if (hasErrors) {
            Swal.fire("",
                lang === "eng"
                    ? "Please complete all required fields correctly."
                    : "Por favor complete todos los campos requeridos correctamente.",
                "warning"
            );
        } else {
            // Crear descripción para la reservación
            const description = `RCI Transroute Reservation ${idBooking} to: ${form.fullname}, Email: ${form.email}, Trip Type: ${form.trip_type}, Pickup Location: ${form.pickup_location}, Destination: ${form.destination_location}`;
            localStorage.setItem("desc", description);
            setModalConfirmation(!modalConfirmation);
        }
    };

    // Estado para el tab activo en el modal de confirmación
    const [activeTab] = useState("1");

    // Guardar reservación en la base de datos
    const handlePayNow = () => {
        setLoaderController(true);

        // Preparar datos para enviar
        const dataToSend = {
            folio: idBooking,
            payment_id: 'Pendiente',
            trip_type: form.trip_type,
            unit: form.unit,
            pickup_location: form.pickup_location,
            destination_location: form.destination_location,
            total_passengers: form.total_passengers,
            fullname: form.fullname,
            member_id: form.member_id,
            email: form.email,
            cellphone: form.cellphone,
            arrival_datetime: form.pickup_location === 'Airport SJD' ? `${form.pickup_date} ${form.pickup_time}` : null,
            arrival_airline: form.pickup_location === 'Airport SJD' ? form.arrival_airline : null,
            arrival_flight_number: form.pickup_location === 'Airport SJD' ? form.arrival_flight_number : null,
            departure_datetime: form.departure_date && form.departure_flight_time ? `${form.departure_date} ${form.departure_flight_time}` : null,
            departure_airline: form.departure_airline || null,
            departure_flight_number: form.departure_flight_number || null,
            hotel_departure_time: form.departure_pickup_time_hotel || null,
            extra_service: Object.keys(serviceSelected || {}).length > 0 ? JSON.stringify(serviceSelected) : null,
            observations: form.observations || '',
            payment_method: "pending",
            discount_code: "0",
            discount_percent: promotion ? Number(promotion) : 0,

            total_payment: Number(totalPayment.toFixed(0)),  // Valor exacto sin redondear para el procesamiento del pago
        };

        // Enviar datos al servidor
        postReservation(dataToSend)
            .then((res) => {
                if(res.status === 200){
                    // Redirigir a la página de checkout
                    history.push({
                        pathname: `/checkout/${idBooking}`,
                        state: {form, lang}
                    });
                    window.location.reload();
                }
            })
            .catch((error) => {
                // console.error("Error al crear la reservación:", error);
                setLoaderController(false);
                Swal.fire(
                    lang === "eng" ? "Oops!" : "¡Ups!",
                    lang === "eng"
                        ? "Something went wrong when creating reservation... try again later."
                        : "Algo salió mal al crear la reservación... inténtelo más tarde.",
                    "error"
                );
            });
    };
    // console.log('totalPayment', totalPayment);
    // console.log('form', form);
    // Formulario de información de viaje
    const formTravelInformation = () => {
        return (
            <div>
                <h3 className="subtitle">{lang === "eng" ? "Select your Transportation Service:" : "Selecciona tu servicio de transportación:"}</h3>
                <FormGroup>
                    <Label>{lang === "eng" ? "Select service" : "Selecciona servicio"}</Label>
                    <InputGroup className="tr-select">
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <FontAwesomeIcon icon="route" />
                            </InputGroupText>
                        </InputGroupAddon>
                        <Select
                            value={auxTripType}
                            classNamePrefix="tr"
                            className="select-transroute"
                            options={tripValidator ? tripTypes2 : tripTypes}
                            onChange={handleChangeTripType}
                        />
                    </InputGroup>
                </FormGroup>
                <Row className="px-0">
                    <FormGroup className="col-12 col-lg-6">
                        <Label>{lang === "eng" ? "Select vehicle transport" : "Seleccione vehículo de transporte"}</Label>
                        <InputGroup className="tr-select">
                            <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                    <FontAwesomeIcon icon="shuttle-van" />
                                </InputGroupText>
                            </InputGroupAddon>
                            <Select value={auxUnit} classNamePrefix="tr" className="select-transroute" options={unitsOptions} onChange={handleChangeVehicle} />
                        </InputGroup>
                    </FormGroup>
                    <FormGroup className="col-12 col-lg-6">
                        <Label>{lang === 'eng' ? 'Passengers number:' : 'Número de pasajeros:'}</Label>
                        <InputGroup>
                            <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                    <FontAwesomeIcon icon="user-friends" />
                                </InputGroupText>
                            </InputGroupAddon>
                            <Input type="select" id="total_passengers" onChange={handleChange} name="total_passengers">
                                {
                                    numPassengers.map((item, pos) => (
                                        <option key={pos}>{item}</option>
                                    ))
                                }
                            </Input>
                        </InputGroup>
                    </FormGroup>
                </Row>
                <FormGroup>
                    <Label>{lang === "eng" ? "Pickup location:" : "Ubicación de recogida:"}</Label>
                    <InputGroup className="tr-select">
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <FontAwesomeIcon icon="map-marker-alt" />
                            </InputGroupText>
                        </InputGroupAddon>
                        <Select
                            value={auxPickup}
                            classNamePrefix="tr"
                            className={`select-transroute ${formSubmitted && validationErrors.pickup_location ? 'is-invalid' : ''}`}
                            options={filteredPickupLocations}
                            onChange={handleChangePickup}
                            placeholder={lang === "eng" ? "Select pickup location" : "Seleccione ubicación de recogida"}
                            isDisabled={form.trip_type === "Round Trip"}
                        />
                    </InputGroup>
                    {form.trip_type === "Round Trip" && (
                        <div className="text-info mt-2" style={{fontSize: '0.875rem'}}>
                            <FontAwesomeIcon icon="info-circle" className="me-1" />
                            {lang === "eng"
                                ? "For Round Trip services, pickup location is automatically set to Airport SJD"
                                : "Para servicios de Ida y Vuelta, la ubicación de recogida se establece automáticamente en Airport SJD"
                            }
                        </div>
                    )}
                    {formSubmitted && validationErrors.pickup_location && (
                        <div className="invalid-feedback" style={{display: 'block'}}>
                            {lang === "eng" ? "Please select a pickup location" : "Por favor seleccione una ubicación de recogida"}
                        </div>
                    )}
                </FormGroup>
                <FormGroup>
                    <Label>{lang === "eng" ? "Destination location:" : "Ubicación de destino:"}</Label>
                    <InputGroup className="tr-select">
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <FontAwesomeIcon icon="map-marker-alt" />
                            </InputGroupText>
                        </InputGroupAddon>
                        <Select value={auxDestination}
                            classNamePrefix="tr"
                            className={`select-transroute ${formSubmitted && validationErrors.destination_location ? 'is-invalid' : ''}`}
                            options={locations}
                            onChange={handleChangeDestination}
                        />
                    </InputGroup>
                    {formSubmitted && validationErrors.destination_location && (
                        <div className="invalid-feedback" style={{display: 'block'}}>
                            {lang === "eng" ? "Please select a destination location" : "Por favor seleccione una ubicación de destino"}
                        </div>
                    )}
                </FormGroup>
                <DateTimePickerForm
                    tripValidator={tripValidator}
                    isOneWay={isOneWay}
                    isRoundTrip={isRoundTrip}
                    isOneWayAirportDestination={isOneWayAirportDestination}
                    lang={lang}
                    form={form}
                    setForm={setForm}
                    validationErrors={validationErrors}
                    setValidationErrors={setValidationErrors}
                    formSubmitted={formSubmitted}
                />
            </div>
        );
    };

    const formContactInformation = () => {
        return (
            <Fragment>
                <h3 className="subtitle">{lang === "eng" ? "Contact / RCI Member Information" : "Información de Contacto / Miembro RCI :"}</h3>
                <FormGroup className="col-12 col-lg-6 p-0">
                    <Label>{lang === "eng" ? "Enter the RCI Member ID" : "Ingrese el identificador del miembro RCI"}</Label>
                    <InputGroup>
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <FontAwesomeIcon icon="id-card" />
                            </InputGroupText>
                        </InputGroupAddon>
                        <Input
                            type="text"
                            onChange={handleChange}
                            name="member_id"
                            value={form.member_id}
                            invalid={formSubmitted && validationErrors.member_id}
                        />
                        {formSubmitted && validationErrors.member_id && (
                            <FormFeedback>
                                {lang === "eng" ? "Please enter the RCI Member ID" : "Por favor ingrese el identificador del miembro RCI"}
                            </FormFeedback>
                        )}
                    </InputGroup>
                </FormGroup>
                <FormGroup>
                    <Label>{lang === "eng" ? "Enter the member ‘s full name" : "Ingrese el nombre completo del Miembro"}</Label>
                    <InputGroup>
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <FontAwesomeIcon icon="user" />
                            </InputGroupText>
                        </InputGroupAddon>
                        <Input
                            type="text"
                            onChange={handleChange}
                            name="fullname"
                            value={form.fullname}
                            invalid={formSubmitted && validationErrors.fullname}
                        />
                        {formSubmitted && validationErrors.fullname && (
                            <FormFeedback>
                                {lang === "eng" ? "Please enter the traveler's full name" : "Por favor ingrese el nombre completo del miembro"}
                            </FormFeedback>
                        )}
                    </InputGroup>
                </FormGroup>
                
                <Row className="px-0">
                    <FormGroup className="col-12 col-lg-6">
                        <Label>{lang === "eng" ? "Enter main cell number:" : "Ingrese número de contacto:"}</Label>
                        <InputGroup>
                            <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                    <FontAwesomeIcon icon="phone" />
                                </InputGroupText>
                            </InputGroupAddon>
                            <Input
                                type="text"
                                maxLength="10"
                                required
                                onChange={handleChange}
                                name="cellphone"
                                value={form.cellphone}
                                invalid={formSubmitted && validationErrors.cellphone}
                            />
                            {formSubmitted && validationErrors.cellphone && (
                                <FormFeedback>
                                    {lang === "eng" ? "Please enter a valid 10-digit phone number" : "Por favor ingrese un número de teléfono válido de 10 dígitos"}
                                </FormFeedback>
                            )}
                        </InputGroup>
                    </FormGroup>
                    <FormGroup className="col-12 col-lg-6">
                        <Label>{lang === "eng" ? "Enter the contact's e-mail" : "Ingresa el e-mail del contacto"}</Label>
                        <InputGroup>
                            <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                    <FontAwesomeIcon icon="envelope" />
                                </InputGroupText>
                            </InputGroupAddon>
                            <Input
                                type="email"
                                onChange={handleChange}
                                name="email"
                                value={form.email}
                                invalid={formSubmitted && validationErrors.email}
                            />
                        </InputGroup>
                        {formSubmitted && validationErrors.email && (
                            <FormFeedback>
                                {lang === "eng" ? "Please enter a valid email address" : "Por favor ingrese una dirección de correo electrónico válida"}
                            </FormFeedback>
                        )}
                    </FormGroup>
                </Row>
                {
                    !tripValidator && (<>
                        <h3 className="my-3">{lang === "eng" ? "Arrival Information:" : "Información de llegada:"}</h3>
                        <Row className="px-0">
                            <FormGroup className="col-12 col-lg-6">
                                <Label>{lang === "eng" ? "Airline's name:" : "Nombre de la aerolínea:"}</Label>
                                <InputGroup className="tr-input">
                                    <InputGroupAddon addonType="prepend">
                                        <InputGroupText>
                                            <FontAwesomeIcon icon="plane-arrival" />
                                        </InputGroupText>
                                    </InputGroupAddon>
                                    <Select
                                        value={auxArrivalAirline}
                                        classNamePrefix="tr"
                                        className={`select-transroute ${formSubmitted && validationErrors.arrival_airline ? 'is-invalid' : ''}`}
                                        options={airlinesOptions}
                                        onChange={handleChangeArrivalAirline}
                                    />
                                </InputGroup>
                                {formSubmitted && validationErrors.arrival_airline && (
                                    <div className="invalid-feedback" style={{display: 'block'}}>
                                        {lang === "eng" ? "Please select an airline" : "Por favor seleccione una aerolínea"}
                                    </div>
                                )}
                            </FormGroup>
                            <FormGroup className="col-12 col-lg-6">
                                <Label>{lang === "eng" ? "Flight's number:" : "Número de vuelo:"}</Label>
                                <InputGroup>
                                    <InputGroupAddon addonType="prepend">
                                        <InputGroupText>
                                            <FontAwesomeIcon icon="plane-arrival" />
                                        </InputGroupText>
                                    </InputGroupAddon>
                                    <Input
                                        type="text"
                                        onChange={handleChange}
                                        name="arrival_flight_number"
                                        value={form.arrival_flight_number}
                                        invalid={formSubmitted && validationErrors.arrival_flight_number}
                                    />
                                    {formSubmitted && validationErrors.arrival_flight_number && (
                                        <FormFeedback>
                                            {lang === "eng" ? "Please enter the flight number" : "Por favor ingrese el número de vuelo"}
                                        </FormFeedback>
                                    )}
                                </InputGroup>
                            </FormGroup>
                        </Row>
                    </>)
                }
                <div className="departure-information">
                    {
                        (form.trip_type === 'Round Trip') || (isOneWayAirportDestination) ? <>
                        <Fragment>
                            <h3 className="my-3">{lang === "eng" ? "Departure Information:" : "Información de partida:"}</h3>
                            <Row className="px-0">
                                <FormGroup className="col-12 col-lg-6">
                                    <Label>{lang === "eng" ? "Airline's name:" : "Nombre de la aerolínea:"}</Label>
                                    <InputGroup className="tr-input">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <FontAwesomeIcon icon="plane-departure" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Select
                                            value={auxDepartureAirline}
                                            classNamePrefix="tr"
                                            className={`select-transroute ${formSubmitted && validationErrors.departure_airline ? 'is-invalid' : ''}`}
                                            options={airlinesOptions}
                                            onChange={handleChangeDepartureAirline}
                                            placeholder="Type or select"
                                        />
                                    </InputGroup>
                                    {formSubmitted && validationErrors.departure_airline && (
                                        <div className="invalid-feedback" style={{display: 'block'}}>
                                            {lang === "eng" ? "Please select an airline" : "Por favor seleccione una aerolínea"}
                                        </div>
                                    )}
                                </FormGroup>
                                <FormGroup className="col-12 col-lg-6">
                                    <Label>{lang === "eng" ? "Flight's number:" : "Número de vuelo:"}</Label>
                                    <InputGroup>
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <FontAwesomeIcon icon="plane-departure" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            type="text"
                                            onChange={handleChange}
                                            name="departure_flight_number"
                                            value={form.departure_flight_number}
                                            invalid={formSubmitted && validationErrors.departure_flight_number}
                                        />
                                    </InputGroup>
                                    {formSubmitted && validationErrors.departure_flight_number && (
                                        <FormFeedback>
                                            {lang === "eng" ? "Please enter the flight number" : "Por favor ingrese el número de vuelo"}
                                        </FormFeedback>
                                    )}
                                </FormGroup>
                            </Row>
                            {form.departure_pickup_time_hotel && (
                                <>
                                    <Row className="px-0">
                                        <FormGroup className="col-6">
                                            <Label>{lang === 'eng' ? 'Hotel Pickup Time:' : 'Hora de recogida en hotel:'}</Label>
                                            <InputGroup>
                                                <InputGroupAddon addonType="prepend">
                                                    <InputGroupText>
                                                        <i className="fa fa-clock-o" aria-hidden="true"></i>
                                                    </InputGroupText>
                                                </InputGroupAddon>
                                                <input
                                                    type="text"
                                                    value={form.departure_pickup_time_hotel || ''}
                                                    disabled
                                                    className="form-control"
                                                    style={{
                                                        backgroundColor: '#f8f9fa',
                                                        border: '1px solid #ddd'
                                                    }}
                                                />
                                            </InputGroup>
                                        </FormGroup>
                                    </Row>
                                    <Row className="px-0">
                                        <FormGroup className="col-12 form-group warning-hotel-pickup">
                                            <div className="alert alert-info text-center p-2 m-0">
                                                {lang === 'eng'
                                                    ? `NOTE: Hotel pickup time is 3 hours before the flight departure: ${form.departure_pickup_time_hotel}`
                                                    : `Hora de recogida en hotel son 3 horas antes de la salida del vuelo ${form.departure_pickup_time_hotel}`}
                                            </div>
                                        </FormGroup>
                                    </Row>
                                </>
                            )}
                        </Fragment>
                        </> : null
                    }
                </div>
                <Row className="px-0 my-3">
                    <Col xs={12}>
                    <GetStopSuperMarket
                        data={form}
                        totalPayment={totalPayment}
                        setTotalPayment={setTotalPayment}
                        setIsStopatStore={setIsStopatStore}
                        isServiceActive={isServiceActive}
                        setIsServiceActive={setIsServiceActive}
                        serviceSelected={serviceSelected}
                        setServiceSelected={setServiceSelected}
                    />
                    </Col>
                </Row>
                <Row className="px-0 my-3">
                    <Col xs={12}>
                        <FormGroup>
                            <h3>{lang === "eng" ? "Observations:" : "Observaciones"}</h3>
                            <small>{lang === "eng" ? "Do you have any observations of special request?" : "¿Tienes alguna observación o petición especial?"}</small>
                            <InputGroup>
                                <Input className="textarea-observations" type="textarea" name="observations" value={form.observations} onChange={handleChange} />
                            </InputGroup>
                        </FormGroup>
                    </Col>
                    <Col xs={12}>
                        <div className="mt-12">
                            <Button className="my-2 w-50" onClick={toggleModalConfirmation} color="primary">
                                <FontAwesomeIcon className="mr-2" icon="calendar-check" />
                                {lang === "eng" ? "CONFIRM RESERVATION" : "CONFIRMAR RESERVACIÓN"}
                            </Button>
                        </div>
                    </Col>
                    {/** Antes aqui estaba el metodo de pago */}
                </Row>
            </Fragment>
        );
    };

    return (
        <Fragment>
            <LoadingOverlay active={loaderController} spinner text="Processing data...">
                <Container>
                    <Row>
                         <Col xs={12}>
                            {/* <img className="img-fluid" src="https://picsum.photos/id/12/1200/200" /> */}
                            <h1>{lang === "eng" ? "Book online your Ground Transportation" : "Reserva tu transportación"}</h1>
                        </Col>
                        <Col xs={12} md={6} lg={8}>
                            <Form className="booking-form">
                                <div className="booking-step1">
                                    {formTravelInformation()}

                                    <Row className="px-0 my-3">
                                        {firstFormValidator && (
                                            <Col xs={12}>
                                                <Alert color="danger" className="text-center">
                                                    {lang === "eng" ? "You need to complete this section to continue." : "Necesitas completar esta sección para continuar."}
                                                </Alert>
                                            </Col>
                                        )}
                                        {!validRate && (
                                            <Col xs={12}>
                                                <Alert color="danger" className="text-center">
                                                    {lang === "eng" ? "You need to choose a pickup and destination valid to continue." : "Necesitas seleccionar una tarifa correcta para continuar."}
                                                </Alert>
                                            </Col>
                                        )}
                                        {isCalculatingRate && (
                                            <Col xs={12}>
                                                <Alert color="info" className="text-center">
                                                    {lang === "eng" ? "Calculating rates..." : "Calculando tarifas..."}
                                                </Alert>
                                            </Col>
                                        )}
                                    </Row>
                                </div>
                                {formContactInformation()}
                            </Form>
                        </Col>
                        {/* ////  DETAILS SIDEBAR */}
                        <Col xs={12} md={6} lg={4}>
                            <Sidebar
                                isOneWay={isOneWay}
                                isRoundTrip={isRoundTrip}
                                isOneWayAirportDestination={isOneWayAirportDestination}
                                setForm={setForm}
                                data={form}
                                unit={unitSelected}
                                lang={lang}
                                totalPayment={totalPayment}
                                setTotalPayment={setTotalPayment}
                                tripValidator={tripValidator}
                                isServiceActive={isServiceActive}
                                serviceSelected={serviceSelected}
                                isCalculatingRate={isCalculatingRate}
                                tarifaBase={tarifaBase}
                            />
                        </Col>
                    </Row>
                    <Modal isOpen={modalConfirmation} toggle={toggleModalConfirmation} className="modal-lg">
                        <ModalBody>
                            <Summary
                                lang={lang}
                                title={lang === "eng" ? "Please Confirm your reservation" : "Por favor confirma tu reservación"}
                                data={form}
                                unit={unitSelected}
                                totalPayment={totalPayment}
                                isOneWayAirportDestination={isOneWayAirportDestination}
                                isServiceActive={isServiceActive}
                                tripValidator={tripValidator}
                                isRoundTrip={isRoundTrip}
                                serviceSelected={serviceSelected}
                                isCalculatingRate={isCalculatingRate}
                                tarifaBase={tarifaBase}
                            />

                                <Col xs={12} className="payment">
                                {/* <h3 className="my-3">
                                    <FontAwesomeIcon icon="credit-card" className="mr-3" /> {lang === "eng" ? "Payment details" : "Método de Pago"}
                                </h3> */}
                                {/*  <Nav tabs>
                                    <NavItem>
                                        <NavLink
                                            className={classnames({ active: activeTab === '1' })}
                                            onClick={() => { toggle('1'); }}
                                        >
                                            <FontAwesomeIcon icon="credit-card" /><br />
                                            {lang === 'eng' ? 'CREDIT CARD' : 'TARJETA DE CRÉDITO'}
                                        </NavLink>
                                    </NavItem>
                                </Nav> */}
                                <TabContent activeTab={activeTab}>
                                    <TabPane tabId="1">
                                        <Row>
                                            <Col xs="12">
                                                <div className="pymnt-itm active">
                                                    <div className="pymnt-cntnt">
                                                        <Button style={{width:'100%'}} color="primary" onClick={() => handlePayNow()}>
                                                            <span style={{color:'#ffffff', textDecoration:'none'}}>PAY NOW</span></Button>
                                                        {/*<PaymentPaypal/>
                                                        {/* <iframe src={`${configs.URL_PAYMENT_PAYPAL}/?paypal=true&description=${localStorage.getItem('desc')}&idReservation=${localStorage.getItem('idReservation')}&pickupId=${form.pickup_id}&destinationId=${form.destination_id}&transportId=${form.unit_id}&typeTrip=${form.trip_type}&rate=${totalPayment}`} frameBorder="0" style={{height:'350px', overflowY:'auto', width:'100%'}}/> */}
                                                        {/* <PaymentStripe toggleCloseModal={toggleCloseModal} totalPayment={totalPayment} lang={lang} handlePayNow={handlePayNow} /> */}
                                                    </div>
                                                </div>
                                            </Col>
                                        </Row>
                                    </TabPane>
                                    {/* <TabPane tabId="2">
                                        <Row>
                                            <Col xs="12">
                                                <h4>Cash Payment</h4>
                                                <p>This method of payment requires payment in cash when picked up at the airport or Hotel</p>
                                            </Col>
                                        </Row>
                                    </TabPane> */}
                                </TabContent>
                                {/*  <Benefits /> */}
                            </Col>
                        </ModalBody>
                        {/*<ModalFooter>
                           <Button onClick={toggleModalConfirmation} color="secondary">{lang === 'eng' ? 'Cancel' : 'Cancelar'}</Button>
                            <Button color="primary">{lang === 'eng' ? 'Confirm and Send Email Voucher' : 'Confirmar y envíar Voucher por E-mail'}</Button>
                        </ModalFooter>
                        */}
                    </Modal>
                </Container>
            </LoadingOverlay>
        </Fragment>
    );
}
